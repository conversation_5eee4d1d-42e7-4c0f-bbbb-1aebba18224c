import os
import shutil
from pathlib import Path
from typing import Dict, List

from videotrans.configure import config
from videotrans.task._base import BaseTask
from videotrans.task._dubbing import DubbingSrt
from videotrans.task.as_s import Subtitle, SubtitleStyle, VideoFormat, generate_bilingual_ass
from videotrans.util import tools


class ApplySubtitleStyleTask(BaseTask):
    """
    Apply subtitle styling and dubbing to video task
    
    cfg={
        'srt_path': str,  # Local path to SRT file
        'video_path': str,  # Local path to video file
        'voice_separation': bool,
        'source_language': str,
        'target_language': str,
        'subtitle_layout': str,  # 'single' or 'double'
        'subtitle_style': dict,  # Primary and secondary styling
        'dubbing_settings': dict,  # Optional dubbing configuration
        'target_dir': str,
        'cache_folder': str,
        'uuid': str,
        'basename': str,
        'noextname': str
    }
    """

    def __init__(self, cfg: Dict = None, obj: Dict = None):
        super().__init__(cfg, obj)
        # This is a standalone task that doesn't need the full pipeline
        self.shoud_recogn = False
        self.shoud_trans = False
        self.shoud_dubbing = False
        self.shoud_hebing = False
        
        # Set up file paths
        self.srt_file = self.cfg['srt_path']
        self.video_file = self.cfg['video_path']
        self.styled_subtitle_file = None
        self.dubbing_audio_file = None
        
    def prepare(self):
        """Main execution method"""
        try:
            self._signal(text="Starting subtitle style application task")
            config.logger.info(f"ApplySubtitleStyleTask started with config: {self.cfg}")

            # Step 1: Setup and verify files
            self._setup_files()
            if self._exit():
                return

            # Step 2: Apply subtitle styling
            self._apply_subtitle_styling()
            if self._exit():
                return

            # Step 3: Generate dubbing if enabled
            if self.cfg.get('dubbing_settings') and self.cfg['dubbing_settings'].get('enable_dubbing'):
                self._generate_dubbing()
                if self._exit():
                    return

            # Step 4: Merge video with styled subtitles and dubbing
            self._merge_video()
            if self._exit():
                return

            self._signal(text="Task completed successfully", type="succeed")
            self.hasend = True

        except Exception as e:
            error_msg = f"ApplySubtitleStyleTask failed: {str(e)}"
            config.logger.error(error_msg, exc_info=True)
            self._signal(text=error_msg, type="error")
            self.hasend = True
            raise
            
    def _setup_files(self):
        """Setup and verify local file paths"""
        self._signal(text="Setting up files...")
        self.precent = 10
        
        try:
            # Verify files exist
            if not Path(self.srt_file).exists():
                raise Exception(f"SRT file not found: {self.srt_file}")
            if not Path(self.video_file).exists():
                raise Exception(f"Video file not found: {self.video_file}")
                
            self._signal(text="Files verified")
            self.precent = 20
            
        except Exception as e:
            raise Exception(f"Failed to setup files: {str(e)}")
            
    def _apply_subtitle_styling(self):
        """Apply styling to subtitles"""
        self._signal(text="Applying subtitle styling...")
        self.precent = 40

        try:
            subtitle_style = self.cfg.get('subtitle_style', {})
            subtitle_layout = self.cfg.get('subtitle_layout', 'single')

            # Validate subtitle style configuration
            if not subtitle_style:
                config.logger.warning("No subtitle style provided, using default styling")
                subtitle_style = {
                    'translation': {
                        'fontFamily': 'Arial',
                        'fontSize': 20,
                        'color': '#FFFFFF',
                        'strokeColor': '#000000',
                        'backgroundColor': '#000000',
                        'strokeWidth': 1,
                        'marginV': 10,
                        'showStroke': True,
                        'showBackground': False,
                        'showShadow': False
                    }
                }
                self.cfg['subtitle_style'] = subtitle_style

            config.logger.info(f"Applying subtitle styling with layout: {subtitle_layout}, style keys: {list(subtitle_style.keys())}")

            # Create ASS file with custom styling
            self.styled_subtitle_file = f"{self.cfg['cache_folder']}/styled_subtitle.ass"

            # Choose between single and double subtitle layout
            if subtitle_layout == 'double' and self._has_double_subtitle_styles(subtitle_style):
                self._create_double_subtitle_ass()
            else:
                self._create_single_subtitle_ass()

            self._signal(text="Subtitle styling applied")
            self.precent = 50

        except Exception as e:
            raise Exception(f"Failed to apply subtitle styling: {str(e)}")
            
    def _create_single_subtitle_ass(self):
        """Create ASS file with single subtitle styling"""
        try:
            # Choose which style to use based on layout preference
            subtitle_layout = self.cfg.get('subtitle_layout', 'single')
            subtitle_style = self.cfg.get('subtitle_style', {})

            # Determine which style to use and normalize the configuration
            style_config = self._get_normalized_style_config(subtitle_layout, subtitle_style)

            # Check if we have a bilingual SRT file
            is_bilingual = self._is_bilingual_srt()

            # Use existing tools to convert SRT to ASS first
            maxlen = 40
            tools.srt2ass(self.srt_file, self.styled_subtitle_file, maxlen)

            # Read and modify the generated ASS content
            with open(self.styled_subtitle_file, 'r', encoding='utf-8') as f:
                ass_content = f.readlines()

            # If bilingual, we need to process the subtitle text differently
            if is_bilingual and subtitle_layout == 'single':
                ass_content = self._process_bilingual_ass_content(ass_content, style_config)
            else:
                ass_content = self._apply_style_to_ass_content(ass_content, style_config, subtitle_layout, subtitle_style)

            # Write back the modified content
            with open(self.styled_subtitle_file, 'w', encoding='utf-8') as f:
                f.writelines(ass_content)

            # Log the style configuration for debugging
            config.logger.info(f"Modified ASS with style: font={style_config['font_family']}, size={style_config['font_size']}, primary_color={style_config['color']}, margin_v={style_config['margin_v']}, background={style_config.get('background_color', '&*********')}, show_bg={style_config['show_background']}")

        except Exception as e:
            config.logger.error(f"Error creating single subtitle ASS: {str(e)}")
            raise

    def _get_normalized_style_config(self, subtitle_layout, subtitle_style):
        """Get normalized style configuration based on layout and available styles"""
        if subtitle_layout == 'single' and 'translation' in subtitle_style:
            # For single layout, use translation style
            style_config = subtitle_style['translation']
        elif subtitle_layout == 'double' and 'original' in subtitle_style:
            # For double layout, use original style
            style_config = subtitle_style['original']
        elif 'primary' in subtitle_style:
            # Fallback to old format
            style_config = subtitle_style['primary']
        elif 'secondary' in subtitle_style:
            style_config = subtitle_style['secondary']
        else:
            # Default style
            style_config = {
                'fontFamily': 'Arial',
                'fontSize': 20,
                'color': '&H00FFFFFF',
                'strokeColor': '&*********',
                'backgroundColor': '&*********',
                'strokeWidth': 0,
                'marginV': 10,
                'showStroke': False,
                'showBackground': False,
                'showShadow': False
            }

        # Normalize field names to standard format
        return {
            'font_family': style_config.get('fontFamily', 'Arial'),
            'font_size': style_config.get('fontSize', 20),
            'color': style_config.get('color', '&H00FFFFFF'),
            'stroke_color': style_config.get('strokeColor', '&*********'),
            'background_color': style_config.get('backgroundColor', '&*********'),
            'stroke_width': style_config.get('strokeWidth', 0),
            'margin_v': style_config.get('marginV', 10),
            'show_stroke': style_config.get('showStroke', False),
            'show_background': style_config.get('showBackground', False),
            'show_shadow': style_config.get('showShadow', False)
        }

    def _is_bilingual_srt(self):
        """Check if the SRT file contains bilingual subtitles (multiple lines per subtitle entry)"""
        try:
            srt_subtitles = tools.get_subtitle_from_srt(self.srt_file)
            # Check if any subtitle has multiple lines (indicating bilingual content)
            for subtitle in srt_subtitles:
                if '\n' in subtitle['text'].strip():
                    return True
            return False
        except Exception as e:
            config.logger.warning(f"Could not determine if SRT is bilingual: {str(e)}")
            return False

    def _has_double_subtitle_styles(self, subtitle_style):
        """Check if we have the required styles for double subtitle layout"""
        # For double layout, we need both 'original' and 'translation' styles
        # OR both 'primary' and 'secondary' styles (legacy format)
        has_new_format = 'original' in subtitle_style and 'translation' in subtitle_style
        has_legacy_format = 'primary' in subtitle_style and 'secondary' in subtitle_style

        result = has_new_format or has_legacy_format
        config.logger.info(f"Double subtitle styles check: new_format={has_new_format}, legacy_format={has_legacy_format}, result={result}")
        return result

    def _normalize_style_config_for_double(self, style_config):
        """Normalize style configuration for double subtitle mode"""
        return {
            'font_family': style_config.get('fontFamily', style_config.get('font_family', 'Arial')),
            'font_size': style_config.get('fontSize', style_config.get('font_size', 20)),
            'color': style_config.get('color', '&H00FFFFFF'),
            'stroke_color': style_config.get('strokeColor', style_config.get('stroke_color', '&*********')),
            'background_color': style_config.get('backgroundColor', style_config.get('background_color', '&*********')),
            'stroke_width': style_config.get('strokeWidth', style_config.get('stroke_width', 0)),
            'margin_v': style_config.get('marginV', style_config.get('margin_v', 10)),
            'show_stroke': style_config.get('showStroke', style_config.get('show_stroke', False)),
            'show_background': style_config.get('showBackground', style_config.get('show_background', False)),
            'show_shadow': style_config.get('showShadow', style_config.get('show_shadow', False))
        }

    def _process_bilingual_ass_content(self, ass_content, style_config):
        """Process ASS content for bilingual subtitles in single layout mode"""
        # For bilingual subtitles in single layout, we want to show only the translation
        # Extract the translation part from each dialogue line
        for i, line in enumerate(ass_content):
            if line.startswith('Dialogue:'):
                # Parse the dialogue line
                parts = line.split(',', 9)  # Split into max 10 parts
                if len(parts) >= 10:
                    text = parts[9].strip()
                    # If text contains multiple lines (\\N), take the last one (translation)
                    if '\\N' in text:
                        lines = text.split('\\N')
                        # Take the last non-empty line as the translation
                        translation = ''
                        for line_text in reversed(lines):
                            if line_text.strip():
                                translation = line_text.strip()
                                break
                        # Reconstruct the dialogue line with only the translation
                        parts[9] = translation + '\n'
                        ass_content[i] = ','.join(parts)

        return self._apply_style_to_ass_content(ass_content, style_config, 'single', {})

    def _apply_style_to_ass_content(self, ass_content, style_config, subtitle_layout, subtitle_style):
        """Apply styling to ASS content"""
        # Extract style configuration
        font_family = style_config.get('font_family', 'Arial')
        font_size = style_config.get('font_size', 20)
        primary_color = style_config.get('color', '&H00FFFFFF')
        stroke_color = style_config.get('stroke_color', '&*********')
        background_color = style_config.get('background_color', '&*********')
        stroke_width = style_config.get('stroke_width', 0)
        margin_v = style_config.get('margin_v', 10)
        show_stroke = style_config.get('show_stroke', False)
        show_background = style_config.get('show_background', False)
        show_shadow = style_config.get('show_shadow', False)

        # Normalize color formats to ensure proper ASS format
        primary_color = self._normalize_ass_color(primary_color)
        stroke_color = self._normalize_ass_color(stroke_color)
        background_color = self._normalize_ass_color(background_color)

        # Apply settings
        outline_width = str(stroke_width) if show_stroke else '0'
        shadow_depth = '2' if show_shadow else '0'
        outline_color = stroke_color if show_stroke else '&*********'

        # Handle background color properly
        if show_background:
            # Ensure background color has proper alpha for visibility
            if background_color == '&*********':
                back_color = '&*********'  # Semi-transparent black
            else:
                back_color = background_color
                # Only modify alpha if the color is fully opaque (starts with &H00)
                # and we want to ensure some transparency for better video rendering
                if len(background_color) == 10 and background_color.startswith('&H00'):
                    # Replace alpha with 80 (50% transparency) but keep the BGR values intact
                    back_color = '&H80' + background_color[4:]  # Replace alpha with 80
                else:
                    # Trust the user's alpha settings for other cases
                    back_color = background_color
        else:
            back_color = '&*********'  # Fully transparent

        # For background to work properly, we need correct BorderStyle
        # BorderStyle: 1=outline+shadow, 3=opaque box, 4=opaque box+outline
        if show_background and show_stroke:
            border_style = '4'  # Opaque box with outline
        elif show_background:
            border_style = '3'  # Opaque box only
        else:
            border_style = '1'  # Normal outline

        # For bilingual subtitles, use different colors for primary and secondary
        # Check if we have both original and translation styles for bilingual display
        secondary_color = primary_color  # Default to same color
        if subtitle_layout == 'double' and 'original' in subtitle_style and 'translation' in subtitle_style:
            # Use translation color as secondary for bilingual display
            translation_style = subtitle_style['translation']
            secondary_color = self._normalize_ass_color(translation_style.get('color', primary_color))

        # Find and replace the Style line
        for i, line in enumerate(ass_content):
            if line.startswith('Style: Default,'):
                # Create new style line with correct field positions
                # Format: Name, Fontname, Fontsize, PrimaryColour, SecondaryColour, OutlineColour, BackColour, Bold, Italic, Underline, StrikeOut, ScaleX, ScaleY, Spacing, Angle, BorderStyle, Outline, Shadow, Alignment, MarginL, MarginR, MarginV, Encoding
                new_style = f"Style: Default,{font_family},{font_size},{primary_color},{secondary_color},{outline_color},{back_color},0,0,0,0,100,100,0,0,{border_style},{outline_width},{shadow_depth},2,10,10,{margin_v},1\n"
                ass_content[i] = new_style

                # Log the applied style for debugging
                config.logger.info(f"Applied ASS style: font={font_family}, size={font_size}, primary_color={primary_color}, secondary_color={secondary_color}, bg={back_color}, show_bg={show_background}, border_style={border_style}, margin_v={margin_v}")
                break

        return ass_content

    def _normalize_ass_color(self, color):
        """Normalize color to proper ASS format (&HBBGGRR or &HAARRGGBB)"""
        if not color:
            return '&H00FFFFFF'

        # Remove any spaces and convert to uppercase
        color = str(color).strip().upper()

        # If it's already in ASS format, validate and return
        if color.startswith('&H'):
            if len(color) == 8:  # &H00BBGGRR format
                return color
            elif len(color) == 10:  # &HAARRGGBB format
                return color
            else:
                config.logger.warning(f"Invalid ASS color format: {color}, using default")
                return '&H00FFFFFF'

        # If it's a hex color (#RRGGBB), convert to ASS format
        if color.startswith('#'):
            if len(color) == 7:
                # Convert #RRGGBB to &H00BBGGRR
                try:
                    r = color[1:3]
                    g = color[3:5]
                    b = color[5:7]
                    return f'&H00{b}{g}{r}'
                except (ValueError, IndexError):
                    config.logger.warning(f"Invalid hex color format: {color}, using default")
                    return '&H00FFFFFF'
            elif len(color) == 9:
                # Convert #AARRGGBB to &HAARRGGBB
                try:
                    a = color[1:3]
                    r = color[3:5]
                    g = color[5:7]
                    b = color[7:9]
                    return f'&H{a}{b}{g}{r}'
                except (ValueError, IndexError):
                    config.logger.warning(f"Invalid hex color with alpha format: {color}, using default")
                    return '&H00FFFFFF'

        # Handle RGB format like "rgb(255,255,255)" or "rgba(255,255,255,1.0)"
        if color.startswith('RGB'):
            try:
                # Extract numbers from rgb() or rgba()
                import re
                numbers = re.findall(r'\d+(?:\.\d+)?', color)
                if len(numbers) >= 3:
                    r = int(float(numbers[0]))
                    g = int(float(numbers[1]))
                    b = int(float(numbers[2]))
                    # Clamp values to 0-255
                    r = max(0, min(255, r))
                    g = max(0, min(255, g))
                    b = max(0, min(255, b))
                    return f'&H00{b:02X}{g:02X}{r:02X}'
            except (ValueError, IndexError):
                config.logger.warning(f"Invalid RGB color format: {color}, using default")

        # Default fallback
        config.logger.warning(f"Unrecognized color format: {color}, using default white")
        return '&H00FFFFFF'

    def _convert_srt_to_subtitle_objects(self, srt_subtitles: List[Dict]) -> List[Subtitle]:
        """Convert SRT subtitle data to Subtitle objects"""
        subtitle_objects = []
        for srt_sub in srt_subtitles:
            # Convert time format from "00:00:01,000" to "00:00:01.000"
            start_time = srt_sub['startraw'].replace(',', '.')
            end_time = srt_sub['endraw'].replace(',', '.')
            text = srt_sub['text']

            subtitle_objects.append(Subtitle(
                start_time=start_time,
                end_time=end_time,
                text=text
            ))
        return subtitle_objects

    def _create_combined_subtitle_style(self, primary_style_config: Dict, secondary_style_config: Dict) -> SubtitleStyle:
        """Create a combined SubtitleStyle from primary and secondary style configurations"""

        # Extract primary style settings
        primary_font_family = primary_style_config.get('fontFamily', 'Arial')
        primary_font_size = primary_style_config.get('fontSize', 20)
        primary_color = primary_style_config.get('color', '#FFFFFF')
        primary_stroke_width = primary_style_config.get('strokeWidth', 0)
        primary_stroke_color = primary_style_config.get('strokeColor', '#000000')
        primary_margin_v = primary_style_config.get('marginV', 40)
        primary_background_color = primary_style_config.get('backgroundColor', '#000000')
        show_primary_stroke = primary_style_config.get('showStroke', False)
        show_primary_shadow = primary_style_config.get('showShadow', False)
        show_primary_background = primary_style_config.get('showBackground', False)

        # Extract secondary style settings
        secondary_font_family = secondary_style_config.get('fontFamily', primary_font_family)
        secondary_font_size = secondary_style_config.get('fontSize', 18)
        secondary_color = secondary_style_config.get('color', '#FFFF00')
        secondary_stroke_width = secondary_style_config.get('strokeWidth', 1)
        secondary_stroke_color = secondary_style_config.get('strokeColor', '#000000')
        secondary_margin_v = secondary_style_config.get('marginV', 0)
        secondary_background_color = secondary_style_config.get('backgroundColor', '#000000')
        show_secondary_stroke = secondary_style_config.get('showStroke', False)
        show_secondary_shadow = secondary_style_config.get('showShadow', False)
        show_secondary_background = secondary_style_config.get('showBackground', False)

        return SubtitleStyle(
            font_family=primary_font_family,
            font_size=primary_font_size,
            primary_color=primary_color,
            primary_stroke_width=primary_stroke_width,
            primary_stroke_color=primary_stroke_color,
            primary_margin_v=primary_margin_v,
            primary_background_color=primary_background_color,
            show_primary_background=show_primary_background,
            secondary_background_color=secondary_background_color,
            secondary_color=secondary_color,
            secondary_font_family=secondary_font_family,
            secondary_font_size=secondary_font_size,
            secondary_stroke_color=secondary_stroke_color,
            secondary_stroke_width=secondary_stroke_width,
            secondary_margin_v=secondary_margin_v,
            shadow_color='#000000',
            show_primary_shadow=show_primary_shadow,
            show_primary_stroke=show_primary_stroke,
            show_secondary_background=show_secondary_background,
            show_secondary_shadow=show_secondary_shadow,
            show_secondary_stroke=show_secondary_stroke
        )

    def _split_bilingual_subtitles(self, srt_subtitles: List[Dict]) -> tuple[List[Subtitle], List[Subtitle]]:
        """Split bilingual SRT subtitles into source and translation subtitle lists"""
        src_subtitles = []
        trans_subtitles = []

        for srt_sub in srt_subtitles:
            start_time = srt_sub['startraw'].replace(',', '.')
            end_time = srt_sub['endraw'].replace(',', '.')
            text = srt_sub['text']

            if '\n' in text:
                # Bilingual subtitle - split into original and translation
                lines = text.split('\n')
                original_text = lines[0].strip() if len(lines) > 0 else ''
                translation_text = lines[-1].strip() if len(lines) > 1 else ''

                src_subtitles.append(Subtitle(
                    start_time=start_time,
                    end_time=end_time,
                    text=original_text
                ))

                trans_subtitles.append(Subtitle(
                    start_time=start_time,
                    end_time=end_time,
                    text=translation_text
                ))
            else:
                # Single language subtitle - use as source
                src_subtitles.append(Subtitle(
                    start_time=start_time,
                    end_time=end_time,
                    text=text
                ))

                # Create empty translation
                trans_subtitles.append(Subtitle(
                    start_time=start_time,
                    end_time=end_time,
                    text=''
                ))

        return src_subtitles, trans_subtitles

    def _create_double_subtitle_ass(self):
        """Create ASS file with double subtitle styling using generate_bilingual_ass"""
        try:
            subtitle_style = self.cfg['subtitle_style']

            # Determine which style format we're using
            if 'original' in subtitle_style and 'translation' in subtitle_style:
                # New format: original and translation
                primary_style_config = subtitle_style['original']
                secondary_style_config = subtitle_style['translation']
                config.logger.info("Using new format: original + translation styles")
            elif 'primary' in subtitle_style and 'secondary' in subtitle_style:
                # Legacy format: primary and secondary
                primary_style_config = subtitle_style['primary']
                secondary_style_config = subtitle_style['secondary']
                config.logger.info("Using legacy format: primary + secondary styles")
            else:
                raise Exception("Double subtitle layout requires both original+translation or primary+secondary styles")

            # Normalize the style configurations
            primary_style = self._normalize_style_config_for_double(primary_style_config)
            secondary_style = self._normalize_style_config_for_double(secondary_style_config)

            # Log the styles for debugging
            config.logger.info(f"Primary style config (original/English): {primary_style}")
            config.logger.info(f"Secondary style config (translation/Vietnamese): {secondary_style}")

            # Read SRT content
            srt_subtitles = tools.get_subtitle_from_srt(self.srt_file)

            # Check if we have bilingual subtitles
            is_bilingual = self._is_bilingual_srt()
            config.logger.info(f"Creating double subtitle ASS with bilingual={is_bilingual}, subtitle_count={len(srt_subtitles)}")

            if is_bilingual:
                # Split bilingual subtitles into source and translation
                src_subtitles, trans_subtitles = self._split_bilingual_subtitles(srt_subtitles)
            else:
                # Convert single language subtitles to source subtitles, empty translations
                src_subtitles = self._convert_srt_to_subtitle_objects(srt_subtitles)
                trans_subtitles = [Subtitle(sub.start_time, sub.end_time, '') for sub in src_subtitles]

            # Create a combined SubtitleStyle from primary and secondary styles
            combined_style = self._create_combined_subtitle_style(primary_style_config, secondary_style_config)

            # Create video format configuration
            video_format = VideoFormat(width=1920, height=720)

            # Generate bilingual ASS content using the new function
            ass_content = generate_bilingual_ass(src_subtitles, trans_subtitles, combined_style, video_format)

            # Write ASS file
            with open(self.styled_subtitle_file, 'w', encoding='utf-8') as f:
                f.write(ass_content)

            config.logger.info(f"Double subtitle ASS file created using generate_bilingual_ass: {self.styled_subtitle_file}")
            config.logger.info(f"Generated {len(src_subtitles)} source subtitles and {len(trans_subtitles)} translation subtitles")

        except Exception as e:
            config.logger.error(f"Error creating double subtitle ASS: {str(e)}")
            raise

    def recogn(self):
        """Required by BaseTask - not used"""
        pass

    def trans(self):
        """Required by BaseTask - not used"""
        pass

    def dubbing(self):
        """Required by BaseTask - not used"""
        pass

    def align(self):
        """Required by BaseTask - not used"""
        pass

    def assembling(self):
        """Required by BaseTask - called after prepare"""
        pass
        
    def task_done(self):
        """Required by BaseTask - cleanup and finalization"""
        pass
        
    def _generate_dubbing(self):
        """Generate dubbing audio from subtitles"""
        self._signal(text="Generating dubbing audio...")
        self.precent = 60

        try:
            dubbing_settings = self.cfg['dubbing_settings']

            # Create dubbing configuration
            dubbing_cfg = {
                'target_sub': self.srt_file,
                'target_wav': f"{self.cfg['cache_folder']}/dubbing_audio.wav",
                'tts_type': dubbing_settings.get('tts_type', 0),
                'voice_role': dubbing_settings.get('voice_role', ''),
                'voice_rate': dubbing_settings.get('voice_rate', '+0%'),
                'volume': dubbing_settings.get('volume', '+0%'),
                'pitch': dubbing_settings.get('pitch', '+0Hz'),
                'target_language_code': dubbing_settings.get('target_language', 'zh-cn'),
                'cache_folder': self.cfg['cache_folder'],
                'uuid': self.uuid,
                'basename': 'subtitle_dubbing',
                'noextname': 'subtitle_dubbing',
                'voice_autorate': False,
                'out_ext': 'wav'
            }

            # Use existing dubbing functionality
            dubbing_task = DubbingSrt(dubbing_cfg)
            dubbing_task.dubbing()
            dubbing_task.align()

            self.dubbing_audio_file = dubbing_cfg['target_wav']
            self._signal(text="Dubbing audio generated")
            self.precent = 70

        except Exception as e:
            raise Exception(f"Failed to generate dubbing: {str(e)}")

    def _merge_video(self):
        """Merge video with styled subtitles and dubbing"""
        self._signal(text="Merging video with subtitles and audio...")
        self.precent = 80

        try:
            output_file = f"{self.cfg['target_dir']}/output_video.mp4"

            # Build ffmpeg command
            cmd = ['-y', '-i', self.video_file]

            # Add dubbing audio if available
            if hasattr(self, 'dubbing_audio_file') and self.dubbing_audio_file and Path(self.dubbing_audio_file).exists():
                cmd.extend(['-i', self.dubbing_audio_file])

            # Set video codec
            cmd.extend(['-c:v', 'libx264'])

            # Handle hard subtitles - embed using ASS file
            if self.styled_subtitle_file and Path(self.styled_subtitle_file).exists():
                # Use subtitles filter to embed hard subtitles (better support for backgrounds)
                escaped_subtitle_path = self.styled_subtitle_file.replace(':', '\\:').replace('\\', '\\\\')
                subtitle_filter = f"subtitles={escaped_subtitle_path}"
                cmd.extend(['-vf', subtitle_filter])

            # Audio settings
            if hasattr(self, 'dubbing_audio_file') and self.dubbing_audio_file and Path(self.dubbing_audio_file).exists():
                cmd.extend(['-c:a', 'aac', '-b:a', '192k'])
                # Map video from first input and audio from second input
                cmd.extend(['-map', '0:v:0', '-map', '1:a:0'])
            else:
                # Copy original audio if no dubbing
                cmd.extend(['-c:a', 'copy'])

            # Quality settings
            cmd.extend(['-crf', str(config.settings.get('crf', 25))])
            cmd.extend(['-preset', config.settings.get('preset', 'medium')])
            cmd.extend(['-movflags', '+faststart'])

            # Output file
            cmd.append(output_file)

            # Execute ffmpeg command
            tools.runffmpeg(cmd)

            self._signal(text="Video processing completed")
            self.precent = 95

            # Verify output file was created
            if Path(output_file).exists():
                self._signal(text="Output video created successfully")
                self.precent = 100
            else:
                raise Exception("Output video file was not created")

        except Exception as e:
            raise Exception(f"Failed to merge video: {str(e)}")

    def _exit(self):
        """Override exit condition to avoid premature exit"""
        # Only exit if explicitly requested or task has ended
        return config.exit_soft or self.hasend
